import tomllib
import pandas as pd
from src.config import Config
from src.logger import get_logger
from src.data.yahoo_fetcher import get_stock_price_timeseries
from src.animation.time_series_animation import create_wealth_animation
from src.utils.title_constructor import construct_string_title
from src.utils.setup import setup_project
from src.utils.paths import get_config_path, get_timestamped_animations_dir


LOGGER = get_logger(__file__)
LOGGER.activate()


if __name__ == "__main__":
    # Set up project directories first
    LOGGER.info("🚀 Initializing ReelStonks...")
    if not setup_project(verbose=True):
        LOGGER.error("❌ Failed to set up project directories. Exiting.")
        exit(1)

    # Load configuration from TOML
    LOGGER.info("📋 Loading configuration file...")
    config_path = get_config_path()
    with open(config_path, "rb") as f:
        config_data = tomllib.load(f)

    LOGGER.info(f" {'*' * 50} ")

    # Initialize Config with values from the TOML file
    config = Config(config_dict=config_data)

    projections = []
    stock_data_cache = {}  # Cache to avoid re-fetching same ticker data

    # Loop through strategies
    for strategy in config.strategies:
        LOGGER.info(
            f"Processing strategy '{strategy.strategy_id}' for {strategy.ticker_symbol} ..."
        )

        # Check if we already have data for this ticker
        if strategy.ticker_symbol not in stock_data_cache:
            # Fetch stock data for ticker
            stock_data = get_stock_price_timeseries(
                strategy.ticker_symbol,
                interval=config.interval,
                start_date=config.start_date_str,
                end_date=config.today_str,
                save_data=config.save_data,
            )
            stock_data_cache[strategy.ticker_symbol] = stock_data
        else:
            stock_data = stock_data_cache[strategy.ticker_symbol]
            LOGGER.info(f" --- Using cached data for {strategy.ticker_symbol}")

        # Get corresponding investment object for this strategy

        if strategy.strategy_id in config.failed_strategies:
            LOGGER.error(
                f"❌ Skipping strategy '{strategy.strategy_id}' due to unsupported investment object."
            )
            continue

        # Initialize investment object
        investment_object = config.investment_objects[strategy.strategy_id]
        investment = investment_object(
            df=stock_data,
            investment_amount=config.investment_amount,
            tax_rate=config.tax_rate,
            tax_free_return_threshold_per_annu=config.tax_free_return_threshold_per_annu,
        )

        # Get wealth projection and append to list as series
        projection = investment.invest(
            strategy.reinvest_dividends, config.data_frequency_timestamp
        )
        projection_series = projection["Wealth"].rename(strategy.display_name)
        projections.append(projection_series)

    LOGGER.info("💰 Concatenating projections and filtering for investment periods...")

    total_investments = projection["Invested Cash"].cumsum().rename("Total Investments")
    projections.append(total_investments)
    projections = pd.concat(projections, axis=1)

    # Create title using strategy display names
    strategy_names = [strategy.display_name for strategy in config.strategies]
    title = construct_string_title(
        strategy_names,
        config.investment_amount,
        config.years_ago,
        config.interval_title,
    )

    # Create animation with dynamic path
    animations_dir = get_timestamped_animations_dir()
    animation_filename = animations_dir / "wealth_animation.mp4"

    create_wealth_animation(
        projections,
        config.years_ago,
        filename=str(animation_filename),
        duration_sec=config.animation_duration_seconds,
        fps=config.fps,
        title=title,
    )
