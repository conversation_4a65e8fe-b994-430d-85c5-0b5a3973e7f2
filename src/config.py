from datetime import datetime
from dateutil.relativedelta import relativedelta
from typing import Dict, Any, Optional
from src.invest import (
    InvestDaily,
    InvestWeekly,
    InvestMonthly,
    InvestQuarterly,
    InvestYearly,
    InvestMonthlyPeaks,
    InvestMonthlyLows,
)
from src.invest.strategy import InvestmentStrategy
from src.utils.dict import get_value_by_key_condition
from src.logger import get_logger


LOGGER = get_logger(__name__)


class Config:
    """
    Configuration class to hold all runtime parameters.
    Accepts optional config_dict for values from a TOML file.
    """

    _INVESTMENT_SCHEMA = {
        ("daily", "regular"): InvestDaily,
        ("weekly", "regular"): InvestWeekly,
        ("monthly", "regular"): InvestMonthly,
        ("quarterly", "regular"): InvestQuarterly,
        ("yearly", "regular"): InvestYearly,
        ("monthly", "peaks"): InvestMonthlyPeaks,
        ("monthly", "lows"): InvestMonthlyLows,
    }
    _INTERVAL_MAP = {
        "daily": "Day",
        "weekly": "Week",
        "monthly": "Month",
        "quarterly": "Quarter",
        "yearly": "Year",
    }

    def __init__(self, config_dict: dict = None):
        """Initialize the Config object with runtime parameters.

        Loads configuration values from a dictionary (typically from a TOML file)
        and sets up all necessary parameters for investment analysis and animation.
        Missing values are filled with sensible defaults.

        Args:
            config_dict (dict, optional): Dictionary containing configuration values.
                Expected structure:
                - ticker (dict): Mapping of ticker symbols to company names
                - general (dict): General configuration with keys:
                    - years_ago (int): Number of years back to analyze
                    - investment_amount (int): Amount to invest per interval
                    - investment_kind (str): Investment frequency ("daily", "weekly", "monthly", "quarterly", "yearly")
                    - animation_duration_seconds (int): Duration of animation in seconds
                    - fps (int): Frames per second for animation
                    - tax_rate (float): Tax rate applied to dividends
                    - tax_free_return_threshold_per_annu (float): Annual tax-free dividend threshold
                    - save_data (bool): Whether to save fetched stock data to CSV files
                - dividends (dict): Mapping of ticker symbols to boolean dividend reinvestment flags
                Defaults to None, which uses built-in defaults.

        Raises:
            ValueError: If investment_kind is not one of the supported values
                ("daily", "weekly", "monthly", "quarterly", "yearly").
        """
        config_dict = config_dict or {}

        # Parse strategies (new format) or fall back to ticker format (old format)
        self.strategies = self._parse_strategies(config_dict)
        self.failed_strategies = []

        # Get general config
        general_cfg = config_dict.get("general", {})
        self.years_ago: int = general_cfg.get("years_ago", 20)
        self.investment_amount: int = general_cfg.get("investment_amount", 1_000)
        self.investment_kind: str = general_cfg.get("investment_kind", "monthly")
        self.animation_duration_seconds: int = general_cfg.get(
            "animation_duration_seconds", 30
        )
        self.fps: int = general_cfg.get("fps", 28)
        self.tax_rate = general_cfg.get("tax_rate", 0.26375)
        self.tax_free_return_threshold_per_annu = general_cfg.get(
            "tax_free_return_threshold_per_annu", 1_000
        )
        self.save_data: bool = general_cfg.get("save_data", False)
        self.data_frequency_timestamp = general_cfg.get(
            "data_frequency_timestamp", "%Y-%m"
        )

        # Maintain backward compatibility with old ticker format
        self.ticker = {
            strategy.strategy_id: strategy.display_name for strategy in self.strategies
        }
        self.dividends = {
            strategy.strategy_id: strategy.reinvest_dividends
            for strategy in self.strategies
        }

        # Derived fields
        self.interval: str = "1d"
        self.interval_title: str = self._INTERVAL_MAP[self.investment_kind]
        self.today: datetime = datetime.now()

        # Use relativedelta for a precise date calculation
        self.start_date: datetime = self.today - relativedelta(years=self.years_ago)

        self.today_str: str = self.today.strftime("%Y-%m-%d")
        self.start_date_str: str = self.start_date.strftime("%Y-%m-%d")

        # Define investment_objects as empty dict
        self.investment_objects = {}

        # Iterate over all unqiue investment stratgeies
        for strategy in self.strategies:

            # Get corresponding investment object for a combination of investment_kind and timing
            _investment_object = get_value_by_key_condition(
                d=self._INVESTMENT_SCHEMA,
                contains=(self.investment_kind, strategy.investment_timing),
            )

            # Log error if investment object is not found
            if not _investment_object:
                LOGGER.error(
                    f"❌ Unsupported 'investment_object' for arguments: {(self.investment_kind, strategy.investment_timing)}. {strategy.strategy_id} skipped."
                )
                self.failed_strategies.append(strategy.strategy_id)
                continue
            self.investment_objects[strategy.strategy_id] = _investment_object

    def _parse_strategies(
        self, config_dict: Dict[str, Any]
    ) -> list[InvestmentStrategy]:
        """Parse investment strategies from configuration.

        Supports both new strategy format and legacy ticker format.

        Args:
            config_dict: Configuration dictionary from TOML file.

        Returns:
            List of InvestmentStrategy objects.
        """
        strategies = []

        # Check for new strategy format
        if "strategies" in config_dict:
            # Get strategy configs
            strategy_configs = config_dict["strategies"]
            # Iterate over strategy configs
            for strategy_id, strategy_config in strategy_configs.items():
                # Initialize strategy object
                strategy = InvestmentStrategy(
                    strategy_id=strategy_id,
                    ticker_symbol=strategy_config["ticker"],
                    display_name=strategy_config.get("name", strategy_config["ticker"]),
                    reinvest_dividends=strategy_config.get("reinvest_dividends", False),
                    investment_timing=strategy_config.get("timing", "regular"),
                    **{
                        k: v
                        for k, v in strategy_config.items()
                        if k not in ["ticker", "name", "reinvest_dividends", "timing"]
                    },
                )
                strategies.append(strategy)
        else:
            # Fall back to legacy ticker format
            ticker_config = config_dict.get("ticker", {"AAPL": "Apple"})
            dividend_config = config_dict.get("dividends", {})

            for ticker_symbol, display_name in ticker_config.items():
                strategy = InvestmentStrategy(
                    strategy_id=ticker_symbol,
                    ticker_symbol=ticker_symbol,
                    display_name=display_name,
                    reinvest_dividends=dividend_config.get(ticker_symbol, False),
                    investment_timing="regular",
                )
                strategies.append(strategy)

        return strategies

    def get_strategy(self, strategy_id: str) -> Optional[InvestmentStrategy]:
        """Get a strategy by its ID.

        Args:
            strategy_id: The strategy identifier.

        Returns:
            InvestmentStrategy object or None if not found.
        """
        for strategy in self.strategies:
            if strategy.strategy_id == strategy_id:
                return strategy
        return None
